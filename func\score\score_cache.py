"""
积分系统缓存管理器
提供Redis缓存功能和分布式锁
"""

import json
import time
from contextlib import contextmanager
from typing import Any, Optional, Dict
from func.log.default_log import DefaultLog

# 尝试导入Redis，如果失败则使用内存缓存
try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None


class ScoreCacheManager:
    """积分系统缓存管理器"""

    def __init__(self, redis_host='localhost', redis_port=6379, redis_db=0):
        """
        初始化缓存管理器
        :param redis_host: Redis主机
        :param redis_port: Redis端口
        :param redis_db: Redis数据库
        """
        self.log = DefaultLog().getLogger()
        self.redis_client = None

        # 初始化内存缓存作为备选方案
        self._memory_cache = {}
        self._cache_timestamps = {}

        if REDIS_AVAILABLE:
            try:
                self.redis_client = redis.Redis(
                    host=redis_host,
                    port=redis_port,
                    db=redis_db,
                    decode_responses=True,
                    socket_timeout=5,
                    socket_connect_timeout=5
                )
                # 测试连接
                self.redis_client.ping()
                self.log.info("Redis缓存连接成功")
            except Exception as e:
                self.log.warning(f"Redis缓存连接失败，将使用内存缓存: {e}")
                self.redis_client = None
        else:
            self.log.info("Redis模块未安装，使用内存缓存")

    def get(self, key: str) -> Optional[Any]:
        """
        获取缓存数据
        :param key: 缓存键
        :return: 缓存值
        """
        try:
            if self.redis_client:
                value = self.redis_client.get(key)
                if value:
                    return json.loads(value)
            else:
                # 使用内存缓存
                if key in self._memory_cache:
                    timestamp = self._cache_timestamps.get(key, 0)
                    if time.time() - timestamp < 300:  # 5分钟过期
                        return self._memory_cache[key]
                    else:
                        # 过期删除
                        del self._memory_cache[key]
                        del self._cache_timestamps[key]
            return None
        except Exception as e:
            self.log.warning(f"获取缓存失败 {key}: {e}")
            return None

    def set(self, key: str, value: Any, ttl: int = 300) -> bool:
        """
        设置缓存数据
        :param key: 缓存键
        :param value: 缓存值
        :param ttl: 过期时间（秒）
        :return: 是否成功
        """
        try:
            if self.redis_client:
                return self.redis_client.setex(key, ttl, json.dumps(value))
            else:
                # 使用内存缓存
                self._memory_cache[key] = value
                self._cache_timestamps[key] = time.time()
                return True
        except Exception as e:
            self.log.warning(f"设置缓存失败 {key}: {e}")
            return False

    def delete(self, key: str) -> bool:
        """
        删除缓存
        :param key: 缓存键
        :return: 是否成功
        """
        try:
            if self.redis_client:
                return bool(self.redis_client.delete(key))
            else:
                # 使用内存缓存
                if key in self._memory_cache:
                    del self._memory_cache[key]
                    del self._cache_timestamps[key]
                    return True
                return False
        except Exception as e:
            self.log.warning(f"删除缓存失败 {key}: {e}")
            return False

    def delete_pattern(self, pattern: str) -> int:
        """
        按模式删除缓存
        :param pattern: 缓存键模式
        :return: 删除数量
        """
        try:
            if self.redis_client:
                keys = self.redis_client.keys(pattern)
                if keys:
                    return self.redis_client.delete(*keys)
                return 0
            else:
                # 使用内存缓存
                import fnmatch
                keys_to_delete = [key for key in self._memory_cache.keys()
                                if fnmatch.fnmatch(key, pattern)]
                for key in keys_to_delete:
                    del self._memory_cache[key]
                    if key in self._cache_timestamps:
                        del self._cache_timestamps[key]
                return len(keys_to_delete)
        except Exception as e:
            self.log.warning(f"按模式删除缓存失败 {pattern}: {e}")
            return 0

    @contextmanager
    def distributed_lock(self, lock_key: str, timeout: int = 30):
        """
        分布式锁
        :param lock_key: 锁键
        :param timeout: 超时时间
        """
        lock_acquired = False
        try:
            if self.redis_client:
                # 使用Redis分布式锁
                lock_acquired = self.redis_client.set(
                    f"lock:{lock_key}",
                    "locked",
                    nx=True,
                    ex=timeout
                )
                if not lock_acquired:
                    raise Exception(f"获取分布式锁失败: {lock_key}")
            else:
                # 简单的内存锁（仅适用于单进程）
                if hasattr(self, '_locks') and lock_key in self._locks:
                    raise Exception(f"获取内存锁失败: {lock_key}")
                if not hasattr(self, '_locks'):
                    self._locks = set()
                self._locks.add(lock_key)
                lock_acquired = True

            yield

        finally:
            if lock_acquired:
                try:
                    if self.redis_client:
                        self.redis_client.delete(f"lock:{lock_key}")
                    else:
                        if hasattr(self, '_locks') and lock_key in self._locks:
                            self._locks.remove(lock_key)
                except Exception as e:
                    self.log.warning(f"释放锁失败 {lock_key}: {e}")

    def cleanup_expired_cache(self):
        """清理过期缓存"""
        try:
            if not self.redis_client:
                # 清理内存缓存中的过期项
                current_time = time.time()
                expired_keys = []
                for key, timestamp in self._cache_timestamps.items():
                    if current_time - timestamp > 300:  # 5分钟过期
                        expired_keys.append(key)

                for key in expired_keys:
                    if key in self._memory_cache:
                        del self._memory_cache[key]
                    if key in self._cache_timestamps:
                        del self._cache_timestamps[key]

                if expired_keys:
                    self.log.info(f"清理过期内存缓存 {len(expired_keys)} 项")
        except Exception as e:
            self.log.error(f"清理过期缓存失败: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            if self.redis_client:
                info = self.redis_client.info()
                return {
                    "type": "redis",
                    "connected": True,
                    "used_memory": info.get("used_memory_human", "unknown"),
                    "connected_clients": info.get("connected_clients", 0),
                    "total_commands_processed": info.get("total_commands_processed", 0)
                }
            else:
                return {
                    "type": "memory",
                    "connected": True,
                    "cache_size": len(self._memory_cache),
                    "timestamp_size": len(self._cache_timestamps)
                }
        except Exception as e:
            return {
                "type": "unknown",
                "connected": False,
                "error": str(e)
            }

    # 兼容性方法
    def get_user_score_cache(self, openId: str) -> Optional[Dict[str, Any]]:
        """获取用户积分缓存（兼容性方法）"""
        return self.get(f"user_score:{openId}")

    def set_user_score_cache(self, openId: str, user_data: Dict[str, Any], ttl: int = 300) -> bool:
        """设置用户积分缓存（兼容性方法）"""
        return self.set(f"user_score:{openId}", user_data, ttl)