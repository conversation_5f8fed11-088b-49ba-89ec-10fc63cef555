"""
积分操作模块
负责积分的增减、充值扣减、事务处理等功能
"""

import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

from ..core.base_manager import BaseManager, CacheableMixin, ValidatorMixin
from ..score_config import ScoreSystemConfig


class ScoreOperations(BaseManager, CacheableMixin, ValidatorMixin):
    """积分操作管理器"""

    def _initialize(self):
        """初始化积分操作管理器"""
        # 初始化缓存相关属性
        if not hasattr(self, '_cache_enabled'):
            self._cache_enabled = True
        if not hasattr(self, '_cache_ttl'):
            self._cache_ttl = 300
        self.log.info("积分操作管理器初始化完成")

    def operate_score(self, openId: str, userName: str, score: int, oper: str,
                     use_transaction: bool = True) -> bool:
        """
        积分操作[加分：聊天  减分：切歌：1分、绘画：1分、唱歌：2分、跳舞：3分、切舞：1分]
        支持事务操作确保数据一致性
        :param openId: 用户开放平台id
        :param userName: 用户名
        :param score: 积分变化量（正数为加分，负数为减分）
        :param oper: 操作积分说明
        :param use_transaction: 是否使用事务
        :return: 操作是否成功
        """
        # 参数验证
        if not self._validate_score_params(openId, userName, score, oper):
            return False

        start_time = time.time()

        # 使用分布式锁防止并发问题
        try:
            with self.db_manager.get_distributed_lock(f"score_operation:{openId}"):
                return self._operate_score_internal(openId, userName, score, oper, use_transaction, start_time)
        except Exception as e:
            self._handle_error(e, "获取分布式锁", openId=openId)
            return False

    def _operate_score_internal(self, openId: str, userName: str, score: int, oper: str,
                               use_transaction: bool, start_time: float) -> bool:
        """内部积分操作实现"""
        try:
            self.log.info(f"开始积分录入：[{openId}][{userName}][{score}][{oper}]")

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 先检查用户是否存在
            user_exists = self.db_manager.users_collection.find_one({"openId": openId}, {"_id": 1})
            if not user_exists:
                self.log.error(f"用户不存在，无法更新积分: {openId}")
                return False

            # 尝试使用事务
            if use_transaction and hasattr(self.db_manager.client, 'start_session'):
                return self._operate_score_with_transaction(openId, userName, score, oper, current_time, start_time)
            else:
                return self._operate_score_without_transaction(openId, userName, score, oper, current_time, start_time)

        except Exception as e:
            self._handle_error(e, "积分操作", openId=openId, userName=userName, score=score, oper=oper)
            return False

    def _operate_score_with_transaction(self, openId: str, userName: str, score: int, oper: str,
                                       current_time: str, start_time: float) -> bool:
        """使用事务的积分操作"""
        try:
            with self.db_manager.client.start_session() as session:
                with session.start_transaction():
                    # 更新用户积分
                    score_data_update = {
                        "$inc": {"score": score},
                        "$set": {"updateTime": current_time}
                    }

                    update_result = self.db_manager.users_collection.update_one(
                        {"openId": openId},
                        score_data_update,
                        session=session
                    )

                    if update_result.matched_count == 0:
                        self.log.error(f"积分更新失败: {openId}")
                        session.abort_transaction()
                        return False

                    # 插入积分操作记录
                    score_record = {
                        "openId": openId,
                        "userName": userName,
                        "score": score,
                        "oper": oper,
                        "submitTime": current_time
                    }

                    self.db_manager.records_collection.insert_one(score_record, session=session)

                    # 清除相关缓存
                    self._clear_score_cache(openId)

                    self._log_operation("积分录入（事务）", True, openId=openId, userName=userName, score=score, oper=oper)
                    return True

        except Exception as e:
            self._handle_error(e, "事务积分操作", openId=openId, score=score)
            return False

    def _operate_score_without_transaction(self, openId: str, userName: str, score: int, oper: str,
                                          current_time: str, start_time: float) -> bool:
        """不使用事务的积分操作（兼容模式）"""
        try:
            # 更新用户积分
            score_data_update = {
                "$inc": {"score": score},
                "$set": {"updateTime": current_time}
            }

            update_result = self.db_manager.users_collection.update_one(
                {"openId": openId},
                score_data_update
            )

            if update_result.matched_count == 0:
                self.log.error(f"积分更新失败: {openId}")
                return False

            # 插入积分操作记录
            score_record = {
                "openId": openId,
                "userName": userName,
                "score": score,
                "oper": oper,
                "submitTime": current_time
            }

            try:
                self.db_manager.records_collection.insert_one(score_record)

                # 清除相关缓存
                self._clear_score_cache(openId)

                self._log_operation("积分录入", True, openId=openId, userName=userName, score=score, oper=oper)
                return True

            except Exception as record_error:
                # 如果记录插入失败，回滚积分更新
                self.log.warning(f"积分记录插入失败，尝试回滚: {str(record_error)}")
                rollback_update = {
                    "$inc": {"score": -score},
                    "$set": {"updateTime": current_time}
                }
                self.db_manager.users_collection.update_one({"openId": openId}, rollback_update)
                return False

        except Exception as e:
            self._handle_error(e, "积分操作", openId=openId, score=score)
            return False

    def batch_update_scores(self, score_updates: List[Dict[str, Any]]) -> Tuple[bool, int, List[str]]:
        """
        批量更新用户积分
        :param score_updates: 积分更新列表，格式：[{"openId": "xxx", "score_change": 10, "oper": "批量更新"}, ...]
        :return: (是否成功, 成功数量, 失败的openId列表)
        """
        try:
            if not score_updates:
                return True, 0, []

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            success_count = 0
            failed_openids = []

            for update_item in score_updates:
                try:
                    openId = update_item.get("openId")
                    score_change = update_item.get("score_change", 0)
                    oper = update_item.get("oper", "批量更新")

                    if not openId or score_change == 0:
                        continue

                    # 获取用户信息
                    user_info = self.db_manager.users_collection.find_one(
                        {"openId": openId},
                        {"userName": 1, "score": 1}
                    )
                    if not user_info:
                        failed_openids.append(openId)
                        self.log.warning(f"批量更新：用户不存在 {openId}")
                        continue

                    # 更新用户积分
                    score_data_update = {
                        "$inc": {"score": score_change},
                        "$set": {"updateTime": current_time}
                    }

                    result = self.db_manager.users_collection.update_one(
                        {"openId": openId},
                        score_data_update
                    )

                    if result.matched_count > 0:
                        # 插入操作记录
                        score_record = {
                            "openId": openId,
                            "userName": user_info.get("userName", ""),
                            "score": score_change,
                            "oper": oper,
                            "submitTime": current_time
                        }

                        try:
                            self.db_manager.records_collection.insert_one(score_record)
                            success_count += 1
                            # 清除缓存
                            self._clear_score_cache(openId)
                        except Exception as record_error:
                            self.log.warning(f"批量更新：记录插入失败 {openId}: {str(record_error)}")
                            # 尝试回滚积分更新
                            self.db_manager.users_collection.update_one(
                                {"openId": openId},
                                {"$inc": {"score": -score_change}}
                            )
                            failed_openids.append(openId)
                    else:
                        failed_openids.append(openId)

                except Exception as item_error:
                    self.log.error(f"批量更新单项失败 {openId}: {str(item_error)}")
                    failed_openids.append(openId)

            self._log_operation("批量更新积分", success_count > 0, success=success_count, failed=len(failed_openids))
            return len(failed_openids) == 0, success_count, failed_openids

        except Exception as e:
            self._handle_error(e, "批量更新积分")
            return False, 0, [item.get("openId", "") for item in score_updates if item.get("openId")]

    def check_score_sufficient(self, openId: str, required_score: int) -> bool:
        """
        检查用户积分是否足够
        :param openId: 用户ID
        :param required_score: 需要的积分
        :return: 是否足够
        """
        try:
            if not self._validate_openid(openId):
                return False

            user_info = self.db_manager.users_collection.find_one(
                {"openId": openId},
                {"score": 1}
            )
            if not user_info:
                return False

            current_score = user_info.get('score', 0)
            return current_score >= required_score

        except Exception as e:
            self._handle_error(e, "检查用户积分", openId=openId, required_score=required_score)
            return False

    def get_user_score(self, openId: str, use_cache: bool = True) -> Optional[int]:
        """
        获取用户当前积分
        :param openId: 用户ID
        :param use_cache: 是否使用缓存
        :return: 用户积分
        """
        try:
            if not self._validate_openid(openId):
                return None

            # 尝试从缓存获取
            if use_cache:
                cache_key = self._get_cache_key("user_score", openId)
                cached_score = self._get_from_cache(cache_key)
                if cached_score is not None:
                    return cached_score

            # 从数据库查询
            user_info = self.db_manager.users_collection.find_one(
                {"openId": openId},
                {"score": 1}
            )

            if user_info:
                score = user_info.get('score', 0)
                # 缓存结果
                if use_cache:
                    cache_key = self._get_cache_key("user_score", openId)
                    self._set_to_cache(cache_key, score, 300)  # 5分钟缓存
                return score

            return None

        except Exception as e:
            self._handle_error(e, "获取用户积分", openId=openId)
            return None

    def _validate_score_params(self, openId: str, userName: str, score: int, oper: str) -> bool:
        """验证积分操作参数"""
        if not self._validate_openid(openId):
            return False

        if not self._validate_username(userName):
            return False

        if not self._validate_score(score):
            return False

        if not oper or not isinstance(oper, str) or len(oper.strip()) == 0:
            self.log.error(f"无效的oper: {oper}")
            return False

        # 验证积分数量是否在允许范围内
        if not ScoreSystemConfig.validate_score_amount(score):
            self.log.error(f"积分数量超出允许范围: {score}")
            return False

        return True

    def _clear_score_cache(self, openId: str) -> None:
        """清除积分相关缓存"""
        try:
            # 清除用户积分缓存
            score_cache_key = self._get_cache_key("user_score", openId)
            self._clear_cache(score_cache_key)

            # 清除用户信息缓存
            user_cache_key = self._get_cache_key("user", openId)
            self._clear_cache(user_cache_key)

            # 清除排行榜缓存
            self._clear_cache("score_rank:*")

        except Exception as e:
            self.log.warning(f"清除积分缓存失败: {e}")

    # 兼容性方法，保持与原有API一致
    def oper_score(self, openId: str, userName: str, score: int, oper: str,
                   use_transaction: bool = True) -> bool:
        """积分操作（兼容性方法）"""
        return self.operate_score(openId, userName, score, oper, use_transaction)